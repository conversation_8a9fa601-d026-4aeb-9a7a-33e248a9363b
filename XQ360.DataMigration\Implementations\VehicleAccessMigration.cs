using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Services.BusinessRules;
using XQ360.DataMigration.Web.Services.DataQuality;
using XQ360.DataMigration.Web.Services.DuplicateDetection;
using XQ360.DataMigration.Web.Services.TransactionManagement;

namespace XQ360.DataMigration.Implementations
{
    public class VehicleAccessMigration : ICoordinatedMigration
    {
        private readonly ILogger<VehicleAccessMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly IPermissionService _permissionService;
        private readonly MigrationReportingService _reportingService;

        // Phase 4 services
        private readonly IBusinessRuleValidationService _businessRuleValidationService;
        private readonly IDataQualityService _dataQualityService;
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IDistributedTransactionService _distributedTransactionService;
        private readonly IGranularRollbackService _granularRollbackService;

        // Thread-safe cache for database lookups to avoid repeated queries
        private readonly ConcurrentDictionary<string, Guid> _siteCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _dealerCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _customerCache = new ConcurrentDictionary<string, Guid>();

        public VehicleAccessMigration(
            ILogger<VehicleAccessMigration> logger,
            IOptions<MigrationConfiguration> config,
            IPermissionService permissionService,
            MigrationReportingService reportingService,
            IBusinessRuleValidationService businessRuleValidationService,
            IDataQualityService dataQualityService,
            IDuplicateDetectionService duplicateDetectionService,
            IDistributedTransactionService distributedTransactionService,
            IGranularRollbackService granularRollbackService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _permissionService = permissionService;
            _reportingService = reportingService;
            _businessRuleValidationService = businessRuleValidationService;
            _dataQualityService = dataQualityService;
            _duplicateDetectionService = duplicateDetectionService;
            _distributedTransactionService = distributedTransactionService;
            _granularRollbackService = granularRollbackService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath, string? accessLevel = null)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;
            var sessionId = Guid.NewGuid();

            try
            {
                _logger.LogInformation("Starting enhanced Vehicle Access migration with Phase 4 capabilities for session {SessionId}", sessionId);

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Phase 4.1.3 - Duplicate Detection and Resolution
                _logger.LogInformation("Phase 4.1.3: Detecting and resolving duplicates...");
                var duplicateResult = await _duplicateDetectionService.DetectDuplicatesAsync(
                    data,
                    _duplicateDetectionService.GetDefaultConfig<CardImportModel>(),
                    CancellationToken.None);

                if (duplicateResult.DuplicateGroups.Any())
                {
                    _logger.LogWarning("Found {DuplicateGroupCount} duplicate groups with {DuplicateCount} total duplicates",
                        duplicateResult.DuplicateGroups.Count, duplicateResult.Statistics.DuplicateEntities);

                    // Resolve duplicates using KeepFirst strategy
                    var resolutionResult = await _duplicateDetectionService.ResolveDuplicatesAsync<CardImportModel>(
                        duplicateResult,
                        DuplicateResolutionStrategy.KeepFirst,
                        CancellationToken.None);

                    if (resolutionResult.Success)
                    {
                        _logger.LogInformation("Successfully resolved duplicates: {KeptCount} kept, {DiscardedCount} discarded",
                            resolutionResult.Statistics.EntitiesKept, resolutionResult.Statistics.EntitiesDiscarded);
                        result.Warnings.Add($"Resolved {resolutionResult.Statistics.EntitiesDiscarded} duplicate records");
                    }
                    else
                    {
                        result.Warnings.Add($"Duplicate resolution had issues: {string.Join(", ", resolutionResult.Errors)}");
                    }
                }

                // Step 3: Phase 4.1.1 - Business Rule Validation
                _logger.LogInformation("Phase 4.1.1: Validating business rules...");
                var validationContext = new ValidationContext
                {
                    SessionId = sessionId,
                    ConnectionString = _connectionString,
                    Mode = ValidationMode.Lenient
                };

                var validationResult = await _businessRuleValidationService.ValidateBatchAsync(
                    data, validationContext, CancellationToken.None);

                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Business rule validation found {ErrorCount} errors and {WarningCount} warnings",
                        validationResult.Summary.TotalErrors, validationResult.Summary.TotalWarnings);

                    result.Warnings.AddRange(validationResult.EntityResults
                        .SelectMany(er => er.Result.Warnings.Select(w => w.Message)));

                    // In lenient mode, continue with warnings but log errors
                    if (validationResult.Summary.TotalErrors > 0)
                    {
                        result.Errors.AddRange(validationResult.EntityResults
                            .SelectMany(er => er.Result.Errors.Select(e => e.Message)));
                    }
                }
                else
                {
                    _logger.LogInformation("Business rule validation passed successfully");
                }

                // Step 4: Phase 4.2.1 - Execute migration with distributed transaction management
                _logger.LogInformation("Phase 4.2.1: Executing migration with distributed transaction management...");

                var transactionOptions = new DistributedTransactionOptions
                {
                    ConnectionString = _connectionString,
                    Timeout = TimeSpan.FromMinutes(30),
                    EnableSavepoints = true,
                    EnableCompensatingActions = true,
                    ValidateConsistency = true
                };

                var transactionResult = await _distributedTransactionService.ExecuteInTransactionAsync(
                    async (transaction, ct) =>
                    {
                        // Create checkpoint before processing
                        var checkpoint = await _granularRollbackService.CreateCheckpointAsync(
                            "before_vehicle_access_migration",
                            sessionId,
                            new CheckpointOptions(),
                            ct);

                        _logger.LogInformation("Created checkpoint {CheckpointId} before migration", checkpoint.CheckpointId);

                        // Execute the actual migration within the distributed transaction
                        return await ExecuteVehicleAccessByAccessLevelAsync(data, transaction.SqlConnection, transaction.SqlTransaction);
                    },
                    transactionOptions,
                    CancellationToken.None);

                if (!transactionResult.Success)
                {
                    _logger.LogError("Distributed transaction failed");
                    result.Success = false;
                    result.Errors.AddRange(transactionResult.Errors);
                    return result;
                }

                var migrationResult = transactionResult.Metadata.ContainsKey("OperationResult")
                    ? (MigrationResult)transactionResult.Metadata["OperationResult"]
                    : new MigrationResult { Success = true };

                // Step 5: Phase 4.1.2 - Data Quality Assessment
                _logger.LogInformation("Phase 4.1.2: Assessing data quality...");
                var qualityScore = await _dataQualityService.CalculateEntityQualityScoreAsync(data, CancellationToken.None);

                _logger.LogInformation("Data quality assessment: Overall score {OverallScore:F1}% ({Grade})",
                    qualityScore.OverallScore, qualityScore.Grade);

                if (qualityScore.OverallScore < 80)
                {
                    result.Warnings.Add($"Data quality score ({qualityScore.OverallScore:F1}%) is below recommended threshold");
                }

                // Step 6: Clean up duplicates (enhanced with Phase 4 capabilities)
                await CleanupDuplicateAccessRecordsAsync();

                // Populate final result
                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors.AddRange(migrationResult.Errors);
                result.Warnings.AddRange(migrationResult.Warnings);

                // Transfer detailed errors and warnings for Full Migration Report
                result.DetailedErrors = migrationResult.DetailedErrors;
                result.DetailedWarnings = migrationResult.DetailedWarnings;

                // Add Phase 4 metadata
                result.Metadata = new Dictionary<string, object>
                {
                    ["SessionId"] = sessionId,
                    ["DataQualityScore"] = qualityScore.OverallScore,
                    ["DataQualityGrade"] = qualityScore.Grade.ToString(),
                    ["DuplicatesDetected"] = duplicateResult.Statistics.DuplicateEntities,
                    ["BusinessRuleValidation"] = validationResult.IsValid,
                    ["TransactionId"] = transactionResult.TransactionId
                };

                _logger.LogInformation("Enhanced Vehicle Access migration completed: {RecordsInserted} inserted, {RecordsSkipped} skipped, Quality: {QualityScore:F1}%, Duration: {Duration}",
                    result.RecordsInserted, result.RecordsSkipped, qualityScore.OverallScore, result.Duration);

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Enhanced Vehicle Access Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Enhanced Vehicle Access migration failed for session {SessionId}", sessionId);

                // Attempt recovery using Phase 4.2.2 capabilities
                try
                {
                    _logger.LogInformation("Attempting automatic recovery...");
                    var recoveryRequest = new RecoveryRequest
                    {
                        SessionId = sessionId,
                        Type = RecoveryType.OperationFailure,
                        Strategy = RecoveryStrategy.Automatic
                    };

                    var recoveryResult = await _granularRollbackService.InitiateRecoveryAsync(recoveryRequest, CancellationToken.None);
                    if (recoveryResult.Success)
                    {
                        _logger.LogInformation("Automatic recovery completed successfully");
                        result.Warnings.Add("Migration failed but automatic recovery was successful");
                    }
                    else
                    {
                        _logger.LogWarning("Automatic recovery failed: {Errors}", string.Join(", ", recoveryResult.Errors));
                        result.Warnings.Add("Migration failed and automatic recovery was unsuccessful");
                    }
                }
                catch (Exception recoveryEx)
                {
                    _logger.LogError(recoveryEx, "Recovery attempt failed");
                    result.Warnings.Add("Migration failed and recovery attempt encountered errors");
                }

                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Warnings = result.Warnings,
                    Duration = DateTime.UtcNow - startTime,
                    Metadata = new Dictionary<string, object> { ["SessionId"] = sessionId }
                };
            }
        }

        /// <summary>
        /// Execute Vehicle Access migration using an external transaction for coordination with other migrations
        /// Enhanced with Phase 4 capabilities
        /// </summary>
        public async Task<MigrationResult> ExecuteWithTransactionAsync(string csvFilePath, SqlConnection connection, SqlTransaction transaction)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;
            var sessionId = Guid.NewGuid();

            try
            {
                _logger.LogInformation("Starting enhanced coordinated Vehicle Access migration for session {SessionId}", sessionId);

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Phase 4.1.3 - Duplicate Detection (but don't resolve in coordinated mode)
                _logger.LogInformation("Phase 4.1.3: Detecting duplicates for coordinated migration...");
                var duplicateResult = await _duplicateDetectionService.DetectDuplicatesAsync(
                    data,
                    _duplicateDetectionService.GetDefaultConfig<CardImportModel>(),
                    CancellationToken.None);

                if (duplicateResult.DuplicateGroups.Any())
                {
                    _logger.LogWarning("Found {DuplicateGroupCount} duplicate groups - will be handled by coordinator",
                        duplicateResult.DuplicateGroups.Count);
                    result.Warnings.Add($"Detected {duplicateResult.Statistics.DuplicateEntities} duplicate records for coordinator review");
                }

                // Step 3: Phase 4.1.1 - Business Rule Validation
                _logger.LogInformation("Phase 4.1.1: Validating business rules for coordinated migration...");
                var validationContext = new ValidationContext
                {
                    SessionId = sessionId,
                    ConnectionString = connection.ConnectionString,
                    Mode = ValidationMode.Strict // Use strict mode for coordinated migrations
                };

                var validationResult = await _businessRuleValidationService.ValidateBatchAsync(
                    data, validationContext, CancellationToken.None);

                if (!validationResult.IsValid)
                {
                    _logger.LogError("Business rule validation failed for coordinated migration");
                    result.Success = false;
                    result.Errors.AddRange(validationResult.EntityResults
                        .SelectMany(er => er.Result.Errors.Select(e => e.Message)));
                    return result;
                }

                // Step 4: Phase 4.2.2 - Create checkpoint for coordinated migration
                _logger.LogInformation("Phase 4.2.2: Creating checkpoint for coordinated migration...");
                var checkpoint = await _granularRollbackService.CreateCheckpointAsync(
                    "coordinated_vehicle_access_migration",
                    sessionId,
                    new CheckpointOptions { IncludeDatabaseSnapshot = true },
                    CancellationToken.None);

                // Step 5: Execute migration with existing transaction
                var migrationResult = await ExecuteVehicleAccessByAccessLevelAsync(data, connection, transaction);

                // Step 6: Phase 4.1.2 - Data Quality Assessment
                _logger.LogInformation("Phase 4.1.2: Assessing data quality for coordinated migration...");
                var qualityScore = await _dataQualityService.CalculateEntityQualityScoreAsync(data, CancellationToken.None);

                _logger.LogInformation("Coordinated migration data quality: {OverallScore:F1}% ({Grade})",
                    qualityScore.OverallScore, qualityScore.Grade);

                // Note: Skipping duplicate cleanup for coordinated transaction - caller should handle this
                // await CleanupDuplicateAccessRecordsAsync();

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                // Add Phase 4 metadata for coordinated migration
                result.Metadata = new Dictionary<string, object>
                {
                    ["SessionId"] = sessionId,
                    ["CheckpointId"] = checkpoint.CheckpointId,
                    ["DataQualityScore"] = qualityScore.OverallScore,
                    ["DataQualityGrade"] = qualityScore.Grade.ToString(),
                    ["DuplicatesDetected"] = duplicateResult.Statistics.DuplicateEntities,
                    ["BusinessRuleValidation"] = validationResult.IsValid,
                    ["CoordinatedMode"] = true
                };

                _logger.LogInformation("Enhanced coordinated Vehicle Access migration completed: {RecordsInserted} inserted, {RecordsSkipped} skipped, Quality: {QualityScore:F1}%, Duration: {Duration}",
                    result.RecordsInserted, result.RecordsSkipped, qualityScore.OverallScore, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Enhanced coordinated Vehicle Access migration failed for session {SessionId}", sessionId);

                // In coordinated mode, let the coordinator handle rollback
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime,
                    Metadata = new Dictionary<string, object>
                    {
                        ["SessionId"] = sessionId,
                        ["CoordinatedMode"] = true,
                        ["RequiresCoordinatorRollback"] = true
                    }
                };
            }
        }

        private async Task<List<CardImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Vehicle Access CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<CardImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return records;
        }

        /// <summary>
        /// Enhanced data processing with Phase 4 validation and quality checks
        /// </summary>
        private async Task<List<CardImportModel>> ProcessCsvFileWithPhase4ValidationAsync(string csvFilePath, Guid sessionId)
        {
            _logger.LogInformation("Processing Vehicle Access CSV file with Phase 4 validation for session {SessionId}...", sessionId);

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<CardImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV file for session {sessionId}");

            // Phase 4.1.2 - Initial data quality assessment
            if (records.Any())
            {
                var initialQualityScore = await _dataQualityService.CalculateEntityQualityScoreAsync(records, CancellationToken.None);
                _logger.LogInformation("Initial data quality score: {Score:F1}% ({Grade})",
                    initialQualityScore.OverallScore, initialQualityScore.Grade);

                if (initialQualityScore.OverallScore < 60)
                {
                    _logger.LogWarning("Initial data quality is below acceptable threshold. Consider data cleansing.");
                }
            }

            return records;
        }

        /// <summary>
        /// Validates a batch of entities using Phase 4 business rules
        /// </summary>
        private async Task<(bool IsValid, List<string> Errors, List<string> Warnings)> ValidateEntitiesWithBusinessRulesAsync<T>(
            IEnumerable<T> entities, Guid sessionId, ValidationMode mode = ValidationMode.Lenient)
        {
            var validationContext = new ValidationContext
            {
                SessionId = sessionId,
                ConnectionString = _connectionString,
                Mode = mode
            };

            var validationResult = await _businessRuleValidationService.ValidateBatchAsync(
                entities, validationContext, CancellationToken.None);

            var errors = validationResult.EntityResults
                .SelectMany(er => er.Result.Errors.Select(e => $"Row {er.EntityIndex}: {e.Message}"))
                .ToList();

            var warnings = validationResult.EntityResults
                .SelectMany(er => er.Result.Warnings.Select(w => $"Row {er.EntityIndex}: {w.Message}"))
                .ToList();

            return (validationResult.IsValid, errors, warnings);
        }

        /// <summary>
        /// Detects and resolves duplicates using Phase 4 capabilities
        /// </summary>
        private async Task<(List<T> CleanedData, int DuplicatesRemoved, List<string> Messages)> DetectAndResolveDuplicatesAsync<T>(
            List<T> data, DuplicateResolutionStrategy strategy = DuplicateResolutionStrategy.KeepFirst)
        {
            if (!data.Any())
                return (data, 0, new List<string>());

            var config = _duplicateDetectionService.GetDefaultConfig<T>();
            var duplicateResult = await _duplicateDetectionService.DetectDuplicatesAsync(data, config, CancellationToken.None);

            var messages = new List<string>();

            if (!duplicateResult.DuplicateGroups.Any())
            {
                messages.Add("No duplicates detected");
                return (data, 0, messages);
            }

            var resolutionResult = await _duplicateDetectionService.ResolveDuplicatesAsync<T>(
                duplicateResult, strategy, CancellationToken.None);

            if (resolutionResult.Success)
            {
                // Extract the resolved entities (this is a simplified approach)
                var resolvedEntities = resolutionResult.ResolvedGroups
                    .Where(g => g.ResolvedEntity != null)
                    .Select(g => (T)g.ResolvedEntity!)
                    .ToList();

                // Add entities that weren't part of any duplicate group
                var duplicateEntityIndices = duplicateResult.DuplicateGroups
                    .SelectMany(g => g.Candidates.Select(c => c.OriginalIndex))
                    .ToHashSet();

                var nonDuplicateEntities = data
                    .Where((entity, index) => !duplicateEntityIndices.Contains(index))
                    .ToList();

                var cleanedData = resolvedEntities.Concat(nonDuplicateEntities).ToList();

                messages.Add($"Resolved {resolutionResult.Statistics.EntitiesDiscarded} duplicates using {strategy} strategy");
                return (cleanedData, resolutionResult.Statistics.EntitiesDiscarded, messages);
            }
            else
            {
                messages.Add($"Duplicate resolution failed: {string.Join(", ", resolutionResult.Errors)}");
                return (data, 0, messages);
            }
        }

        private async Task<MigrationResult> ExecuteVehicleAccessByAccessLevelAsync(List<CardImportModel> data)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var transaction = connection.BeginTransaction();

            try
            {
                var result = await ExecuteVehicleAccessByAccessLevelAsync(data, connection, transaction);
                await transaction.CommitAsync();
                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        private async Task<MigrationResult> ExecuteVehicleAccessByAccessLevelAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction)
        {
            _logger.LogInformation("Grouping records by AccessLevel and executing vehicle access migration...");

            var result = new MigrationResult
            {
                Success = true,
                RecordsProcessed = data.Count,
                Errors = new List<string>(),
                Warnings = new List<string>()
            };

            // Group records by AccessLevel
            var accessLevelGroups = data.GroupBy(d => d.AccessLevel?.ToLower() ?? "department").ToList();

            _logger.LogInformation($"Found {accessLevelGroups.Count} access level groups:");
            foreach (var group in accessLevelGroups)
            {
                _logger.LogDebug($"  - {group.Key}: {group.Count()} records");
            }

            try
            {
                var totalInserted = 0;

                foreach (var group in accessLevelGroups)
                {
                    var accessLevel = group.Key;
                    var groupData = group.ToList();

                    _logger.LogInformation($"Processing {groupData.Count} records for '{accessLevel}' access level...");
                    _logger.LogDebug($"Note: Including both newly created cards and existing cards that were skipped during card creation");

                    var groupResult = await ExecuteVehicleAccessSqlAsync(groupData, accessLevel, connection, transaction);

                    totalInserted += groupResult.RecordsInserted;
                    result.Errors.AddRange(groupResult.Errors);
                    result.Warnings.AddRange(groupResult.Warnings);

                    if (!groupResult.Success)
                    {
                        result.Success = false;
                    }
                }

                // Note: Transaction commit/rollback handled by external caller

                result.RecordsInserted = totalInserted;
                result.RecordsSkipped = result.RecordsProcessed - totalInserted;

                _logger.LogInformation($"Successfully created {totalInserted} vehicle access records across all access levels");

                return result;
            }
            catch (Exception ex)
            {
                // Note: Transaction rollback handled by external caller
                _logger.LogError(ex, "Vehicle Access migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<MigrationResult> ExecuteVehicleAccessSqlAsync(List<CardImportModel> data, string accessLevel, SqlConnection connection, SqlTransaction transaction)
        {
            _logger.LogInformation($"Executing Vehicle Access SQL migration for {accessLevel} level...");

            try
            {
                var insertedCount = 0;
                var warnings = new List<string>();

                switch (accessLevel.ToLower())
                {
                    case "site":
                        insertedCount = await CreateSiteAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "department":
                        insertedCount = await CreateDepartmentAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "model":
                        insertedCount = await CreateModelAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "vehicle":
                        insertedCount = await CreateVehicleAccessAsync(data, connection, transaction, warnings);
                        break;
                    default:
                        throw new ArgumentException($"Invalid access level: {accessLevel}");
                }

                // Note: Transaction commit is handled by the caller

                _logger.LogInformation($"Successfully created {insertedCount} {accessLevel} access records");

                return new MigrationResult
                {
                    Success = true,
                    RecordsInserted = insertedCount,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                // Note: Transaction rollback is handled by the caller
                _logger.LogError(ex, $"Vehicle Access SQL migration failed for {accessLevel} level");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<int> CreateSiteAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Site Vehicle Access records (Comprehensive: Department + Model + Vehicle access)...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);
                    var driverIds = await GetDriverIdsForCardImportAsync(siteGroup.ToList(), connection, transaction);

                    if (driverIds.Count == 0)
                    {
                        warnings.Add($"No drivers found for site {siteGroup.Key.Site}");
                        continue;
                    }

                    // Create parameterized IN clause for driver IDs (SQL injection safe)
                    var driverParameters = string.Join(",", driverIds.Select((_, i) => $"@driverId{i}"));

                    // PART 0: Create SiteVehicleNormalCardAccess (site access itself)
                    var siteAccessSql = $@"
                        INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (Id, SiteId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            @SiteId AS SiteId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[SiteVehicleNormalCardAccess] svnca
                            WHERE svnca.SiteId = @SiteId
                            AND svnca.CardId = d.CardDetailsId
                            AND svnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(siteAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 1: Create DepartmentVehicleNormalCardAccess for all departments in the site
                    var departmentSql = $@"
                        INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (Id, DepartmentId, PermissionId, CardId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            dept.Id AS DepartmentId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[DepartmentVehicleNormalCardAccess] dvnca
                            WHERE dvnca.DepartmentId = dept.Id
                            AND dvnca.CardId = d.CardDetailsId
                            AND dvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(departmentSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 2: Create ModelVehicleNormalCardAccess for all models in all departments of the site
                    var modelSql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            v.DepartmentId AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = v.DepartmentId
                            AND mvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(modelSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 3: Create PerVehicleNormalCardAccess for all vehicles in all departments of the site
                    var vehicleSql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(vehicleSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    _logger.LogDebug($"Created site-level access for {driverIds.Count} drivers in site {siteGroup.Key.Site}");
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create site access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<List<Guid>> GetDriverIdsForCardImportAsync(List<CardImportModel> cardData, SqlConnection connection, SqlTransaction transaction)
        {
            var driverIds = new List<Guid>();

            foreach (var record in cardData)
            {
                try
                {
                    // Enhanced query to find drivers and ensure they have cards
                    var sql = @"
                        SELECT p.DriverId 
                        FROM [dbo].[Person] p
                        INNER JOIN [dbo].[Driver] dr ON p.DriverId = dr.Id
                        INNER JOIN [dbo].[Department] d ON p.DepartmentId = d.Id
                        INNER JOIN [dbo].[Site] s ON d.SiteId = s.Id
                        INNER JOIN [dbo].[Customer] c ON s.CustomerId = c.Id
                        WHERE p.FirstName = @FirstName 
                        AND p.LastName = @LastName
                        AND d.Name = @DepartmentName
                        AND s.Name = @SiteName
                        AND c.CompanyName = @CustomerName
                        AND p.DriverId IS NOT NULL
                        AND dr.CardDetailsId IS NOT NULL";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@FirstName", record.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", record.LastName);
                    cmd.Parameters.AddWithValue("@DepartmentName", record.DepartmentName);
                    cmd.Parameters.AddWithValue("@SiteName", record.Site);
                    cmd.Parameters.AddWithValue("@CustomerName", record.Customer);

                    var result = await cmd.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        var driverId = (Guid)result;
                        driverIds.Add(driverId);
                    }
                    else
                    {
                        _logger.LogWarning($"Driver {record.FirstName} {record.LastName} not found or doesn't have a card assigned");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to get driver ID for {record.FirstName} {record.LastName}: {ex.Message}");
                }
            }

            var uniqueDriverIds = driverIds.Distinct().ToList();
            _logger.LogDebug($"Found {uniqueDriverIds.Count} drivers with cards from {cardData.Count} CSV records");

            return uniqueDriverIds;
        }

        private async Task<int> CreateDepartmentAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Department Vehicle Access records (Comprehensive: Site + Department + Model + Vehicle access)...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by department to get unique department access
            var departmentGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer, Department = d.DepartmentName }).ToList();

            foreach (var deptGroup in departmentGroups)
            {
                try
                {
                    var siteId = await GetSiteIdAsync(deptGroup.Key.Site, deptGroup.Key.Customer, connection, transaction);
                    var driverIds = await GetDriverIdsForCardImportAsync(deptGroup.ToList(), connection, transaction);

                    if (driverIds.Count == 0)
                    {
                        warnings.Add($"No drivers found for department {deptGroup.Key.Department} in site {deptGroup.Key.Site}");
                        continue;
                    }

                    // Create parameterized IN clause for driver IDs (SQL injection safe)
                    var driverParameters = string.Join(",", driverIds.Select((_, i) => $"@driverId{i}"));

                    // PART 1: Create SiteVehicleNormalCardAccess (Dept Level) 
                    var siteAccessSql = $@"
                        INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (Id, SiteId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            @SiteId AS SiteId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[SiteVehicleNormalCardAccess] svnca
                            WHERE svnca.SiteId = @SiteId
                            AND svnca.CardId = d.CardDetailsId
                            AND svnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(siteAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 2: Create DepartmentVehicleNormalCardAccess 
                    var departmentAccessSql = $@"
                        INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (Id, DepartmentId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            p.DepartmentId AS DepartmentId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[DepartmentVehicleNormalCardAccess] dvnca
                            WHERE dvnca.DepartmentId = p.DepartmentId
                            AND dvnca.CardId = d.CardDetailsId
                            AND dvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(departmentAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 3: Create ModelVehicleNormalCardAccess for all models in the department
                    var modelAccessSql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            p.DepartmentId AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = p.DepartmentId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = p.DepartmentId
                            AND mvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(modelAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 4: Create PerVehicleNormalCardAccess for all vehicles in the department
                    var vehicleAccessSql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = p.DepartmentId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(vehicleAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }

                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    _logger.LogDebug($"Created department-level access for {driverIds.Count} drivers in department {deptGroup.Key.Department}");
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create department access for department {deptGroup.Key.Department}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<int> CreateModelAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Model Vehicle Access records...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    // Skip groups with null Site or Customer
                    if (string.IsNullOrEmpty(siteGroup.Key.Site) || string.IsNullOrEmpty(siteGroup.Key.Customer))
                    {
                        warnings.Add($"Skipping group with null/empty Site ({siteGroup.Key.Site}) or Customer ({siteGroup.Key.Customer})");
                        continue;
                    }

                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);

                    // Create parameterized name pairs (SQL injection safe)
                    var nameParameters = string.Join(", ", siteGroup.Select((_, i) => $"(@firstName{i}, @lastName{i})"));

                    var sql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            dept.Id AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND dept.SiteId = @SiteId
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = dept.Id
                            AND mvnca.PermissionId = @PermissionId
                        )
                        AND p.FirstName + ' ' + p.LastName IN (
                            SELECT FirstName + ' ' + LastName 
                            FROM (VALUES {nameParameters}) AS V(FirstName, LastName)
                        );";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                    // Add parameterized name pairs (SQL injection safe)
                    var siteGroupList = siteGroup.ToList();
                    for (int i = 0; i < siteGroupList.Count; i++)
                    {
                        cmd.Parameters.AddWithValue($"@firstName{i}", siteGroupList[i].FirstName);
                        cmd.Parameters.AddWithValue($"@lastName{i}", siteGroupList[i].LastName);
                    }

                    totalInserted += await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create model access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<int> CreateVehicleAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Per Vehicle Access records...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    // Skip groups with null Site or Customer
                    if (string.IsNullOrEmpty(siteGroup.Key.Site) || string.IsNullOrEmpty(siteGroup.Key.Customer))
                    {
                        warnings.Add($"Skipping group with null/empty Site ({siteGroup.Key.Site}) or Customer ({siteGroup.Key.Customer})");
                        continue;
                    }

                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);

                    // Create parameterized name pairs (SQL injection safe)
                    var nameParameters = string.Join(", ", siteGroup.Select((_, i) => $"(@firstName{i}, @lastName{i})"));

                    var sql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND dept.SiteId = @SiteId
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        )
                        AND p.FirstName + ' ' + p.LastName IN (
                            SELECT FirstName + ' ' + LastName 
                            FROM (VALUES {nameParameters}) AS V(FirstName, LastName)
                        );";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);

                    // Add parameterized name pairs (SQL injection safe)
                    var siteGroupList = siteGroup.ToList();
                    for (int i = 0; i < siteGroupList.Count; i++)
                    {
                        cmd.Parameters.AddWithValue($"@firstName{i}", siteGroupList[i].FirstName);
                        cmd.Parameters.AddWithValue($"@lastName{i}", siteGroupList[i].LastName);
                    }

                    totalInserted += await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create vehicle access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.TryGetValue(key, out var cachedSiteId))
                return cachedSiteId;

            var customerId = await GetCustomerIdByNameAsync(customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache.TryAdd(key, siteId);
            return siteId;
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            if (_dealerCache.TryGetValue(dealerName, out var cachedDealerId))
                return cachedDealerId;

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache.TryAdd(dealerName, dealerId);
            return dealerId;
        }

        private async Task<Guid> GetCustomerIdAsync(string customerName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{customerName}|{dealerName}";
            if (_customerCache.TryGetValue(key, out var cachedCustomerId))
                return cachedCustomerId;

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found for dealer '{dealerName}'");

            var customerId = (Guid)result;
            _customerCache.TryAdd(key, customerId);
            return customerId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            return (Guid)result;
        }

        public async Task CleanupDuplicateAccessRecordsAsync()
        {
            _logger.LogInformation("Cleaning up duplicate access records...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Clean up SiteVehicleNormalCardAccess duplicates
                var siteCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, SiteId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [SiteVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [SiteVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(siteCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up DepartmentVehicleNormalCardAccess duplicates
                var departmentCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY CardId, DepartmentId, PermissionId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [DepartmentVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [DepartmentVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(departmentCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up ModelVehicleNormalCardAccess duplicates
                var modelCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, CardId, DepartmentId, ModelId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [ModelVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [ModelVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(modelCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up PerVehicleNormalCardAccess duplicates
                var vehicleCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, VehicleId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [PerVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [PerVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(vehicleCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Successfully cleaned up duplicate access records");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to clean up duplicate access records");
                throw;
            }
        }
    }
}